# cpu_features, a cross platform C99 library to get cpu features at runtime.

load("@bazel_skylib//lib:selects.bzl", "selects")
load("//:bazel/platforms.bzl", "PLATFORM_CPU_ARM", "PLATFORM_CPU_ARM64", "PLATFORM_CPU_MIPS", "PLATFORM_CPU_PPC", "PLATFORM_CPU_X86_64")

package(
    default_visibility = ["//visibility:public"],
    licenses = ["notice"],
)

exports_files(["LICENSE"])

INCLUDES = ["include"]

C99_FLAGS = [
    "-std=c99",
    "-Wall",
    "-Wextra",
    "-Wmissing-declarations",
    "-Wmissing-prototypes",
    "-Wno-implicit-fallthrough",
    "-Wno-unused-function",
    "-Wold-style-definition",
    "-Wshadow",
    "-Wsign-compare",
    "-Wstrict-prototypes",
]

cc_library(
    name = "cpu_features_macros",
    copts = C99_FLAGS,
    includes = INCLUDES,
    textual_hdrs = ["include/cpu_features_macros.h"],
)

cc_library(
    name = "cpu_features_cache_info",
    copts = C99_FLAGS,
    includes = INCLUDES,
    textual_hdrs = ["include/cpu_features_cache_info.h"],
    deps = [":cpu_features_macros"],
)

cc_library(
    name = "bit_utils",
    copts = C99_FLAGS,
    includes = INCLUDES,
    textual_hdrs = ["include/internal/bit_utils.h"],
    deps = [":cpu_features_macros"],
)

cc_test(
    name = "bit_utils_test",
    srcs = ["test/bit_utils_test.cc"],
    includes = INCLUDES,
    deps = [
        ":bit_utils",
        "@com_google_googletest//:gtest_main",
    ],
)

cc_library(
    name = "memory_utils",
    copts = C99_FLAGS,
    includes = INCLUDES,
    textual_hdrs = [
        "src/copy.inl",
        "src/equals.inl",
    ],
)

cc_library(
    name = "string_view",
    srcs = [
        "src/string_view.c",
    ],
    copts = C99_FLAGS,
    includes = INCLUDES,
    textual_hdrs = ["include/internal/string_view.h"],
    deps = [
        ":cpu_features_macros",
        ":memory_utils",
    ],
)

cc_test(
    name = "string_view_test",
    srcs = ["test/string_view_test.cc"],
    includes = INCLUDES,
    deps = [
        ":string_view",
        "@com_google_googletest//:gtest_main",
    ],
)

cc_library(
    name = "filesystem",
    srcs = ["src/filesystem.c"],
    copts = C99_FLAGS,
    includes = INCLUDES,
    textual_hdrs = ["include/internal/filesystem.h"],
    deps = [":cpu_features_macros"],
)

cc_library(
    name = "filesystem_for_testing",
    testonly = 1,
    srcs = [
        "src/filesystem.c",
        "test/filesystem_for_testing.cc",
    ],
    hdrs = [
        "include/internal/filesystem.h",
        "test/filesystem_for_testing.h",
    ],
    defines = ["CPU_FEATURES_MOCK_FILESYSTEM"],
    includes = INCLUDES,
    deps = [
        ":cpu_features_macros",
    ],
)

cc_library(
    name = "stack_line_reader",
    srcs = ["src/stack_line_reader.c"],
    copts = C99_FLAGS,
    defines = ["STACK_LINE_READER_BUFFER_SIZE=1024"],
    includes = INCLUDES,
    textual_hdrs = ["include/internal/stack_line_reader.h"],
    deps = [
        ":cpu_features_macros",
        ":filesystem",
        ":string_view",
    ],
)

cc_test(
    name = "stack_line_reader_test",
    srcs = [
        "include/internal/stack_line_reader.h",
        "src/stack_line_reader.c",
        "test/stack_line_reader_test.cc",
    ],
    defines = ["STACK_LINE_READER_BUFFER_SIZE=16"],
    includes = INCLUDES,
    deps = [
        ":cpu_features_macros",
        ":filesystem_for_testing",
        ":string_view",
        "@com_google_googletest//:gtest_main",
    ],
)

cc_library(
    name = "stack_line_reader_to_use_with_filesystem_for_testing",
    testonly = 1,
    srcs = ["src/stack_line_reader.c"],
    hdrs = ["include/internal/stack_line_reader.h"],
    copts = C99_FLAGS,
    defines = ["STACK_LINE_READER_BUFFER_SIZE=1024"],
    includes = INCLUDES,
    deps = [
        ":cpu_features_macros",
        ":filesystem_for_testing",
        ":string_view",
    ],
)

cc_library(
    name = "hwcaps",
    srcs = ["src/hwcaps.c"],
    copts = C99_FLAGS,
    defines = ["HAVE_STRONG_GETAUXVAL"],
    includes = INCLUDES,
    textual_hdrs = ["include/internal/hwcaps.h"],
    deps = [
        ":cpu_features_macros",
        ":filesystem",
        ":string_view",
    ],
)

cc_library(
    name = "hwcaps_for_testing",
    testonly = 1,
    srcs = [
        "src/hwcaps.c",
        "test/hwcaps_for_testing.cc",
    ],
    hdrs = [
        "include/internal/hwcaps.h",
        "test/hwcaps_for_testing.h",
    ],
    defines = [
        "CPU_FEATURES_MOCK_GET_ELF_HWCAP_FROM_GETAUXVAL",
        "CPU_FEATURES_TEST",
    ],
    includes = INCLUDES,
    deps = [
        ":cpu_features_macros",
        ":filesystem_for_testing",
        ":string_view",
    ],
)

cc_library(
    name = "cpuinfo",
    srcs = selects.with_or({
        PLATFORM_CPU_X86_64: [
            "src/impl_x86_freebsd.c",
            "src/impl_x86_linux_or_android.c",
            "src/impl_x86_macos.c",
            "src/impl_x86_windows.c",
        ],
        PLATFORM_CPU_ARM: ["src/impl_arm_linux_or_android.c"],
        PLATFORM_CPU_ARM64: ["src/impl_aarch64_linux_or_android.c"],
        PLATFORM_CPU_MIPS: ["src/impl_mips_linux_or_android.c"],
        PLATFORM_CPU_PPC: ["src/impl_ppc_linux.c"],
    }),
    copts = C99_FLAGS,
    includes = INCLUDES,
    textual_hdrs = selects.with_or({
        PLATFORM_CPU_X86_64: [
            "src/impl_x86__base_implementation.inl",
            "include/cpuinfo_x86.h",
            "include/internal/cpuid_x86.h",
            "include/internal/windows_utils.h",
        ],
        PLATFORM_CPU_ARM: ["include/cpuinfo_arm.h"],
        PLATFORM_CPU_ARM64: ["include/cpuinfo_aarch64.h"],
        PLATFORM_CPU_MIPS: ["include/cpuinfo_mips.h"],
        PLATFORM_CPU_PPC: ["include/cpuinfo_ppc.h"],
    }) + [
        "src/define_introspection.inl",
        "src/define_introspection_and_hwcaps.inl",
    ],
    deps = [
        ":bit_utils",
        ":cpu_features_cache_info",
        ":cpu_features_macros",
        ":filesystem",
        ":hwcaps",
        ":memory_utils",
        ":stack_line_reader",
        ":string_view",
    ],
)

cc_library(
    name = "cpuinfo_for_testing",
    testonly = 1,
    srcs = selects.with_or({
        PLATFORM_CPU_X86_64: [
            "src/impl_x86_freebsd.c",
            "src/impl_x86_linux_or_android.c",
            "src/impl_x86_macos.c",
            "src/impl_x86_windows.c",
        ],
        PLATFORM_CPU_ARM: ["src/impl_arm_linux_or_android.c"],
        PLATFORM_CPU_ARM64: ["src/impl_aarch64_linux_or_android.c"],
        PLATFORM_CPU_MIPS: ["src/impl_mips_linux_or_android.c"],
        PLATFORM_CPU_PPC: ["src/impl_ppc_linux.c"],
    }),
    hdrs = selects.with_or({
        PLATFORM_CPU_X86_64: [
            "include/cpuinfo_x86.h",
            "include/internal/cpuid_x86.h",
            "include/internal/windows_utils.h",
        ],
        PLATFORM_CPU_ARM: ["include/cpuinfo_arm.h"],
        PLATFORM_CPU_ARM64: ["include/cpuinfo_aarch64.h"],
        PLATFORM_CPU_MIPS: ["include/cpuinfo_mips.h"],
        PLATFORM_CPU_PPC: ["include/cpuinfo_ppc.h"],
    }),
    copts = C99_FLAGS,
    defines = selects.with_or({
        PLATFORM_CPU_X86_64: ["CPU_FEATURES_MOCK_CPUID_X86"],
        "//conditions:default": [],
    }),
    includes = INCLUDES,
    textual_hdrs = selects.with_or({
        PLATFORM_CPU_X86_64: ["src/impl_x86__base_implementation.inl"],
        "//conditions:default": [],
    }) + [
        "src/define_introspection.inl",
        "src/define_introspection_and_hwcaps.inl",
    ],
    deps = [
        ":bit_utils",
        ":cpu_features_cache_info",
        ":cpu_features_macros",
        ":filesystem_for_testing",
        ":hwcaps_for_testing",
        ":memory_utils",
        ":stack_line_reader_to_use_with_filesystem_for_testing",
        ":string_view",
    ],
)

cc_test(
    name = "cpuinfo_test",
    srcs = selects.with_or({
        PLATFORM_CPU_ARM64: ["test/cpuinfo_aarch64_test.cc"],
        PLATFORM_CPU_ARM: ["test/cpuinfo_arm_test.cc"],
        PLATFORM_CPU_MIPS: ["test/cpuinfo_mips_test.cc"],
        PLATFORM_CPU_PPC: ["test/cpuinfo_ppc_test.cc"],
        PLATFORM_CPU_X86_64: ["test/cpuinfo_x86_test.cc"],
    }),
    includes = INCLUDES,
    deps = [
        ":cpuinfo_for_testing",
        ":filesystem_for_testing",
        ":hwcaps_for_testing",
        ":string_view",
        "@com_google_googletest//:gtest_main",
    ],
)

cc_binary(
    name = "list_cpu_features",
    srcs = ["src/utils/list_cpu_features.c"],
    copts = C99_FLAGS,
    includes = INCLUDES,
    deps = [
        ":bit_utils",
        ":cpu_features_macros",
        ":cpuinfo",
    ],
)
