#!/bin/bash

# 🧪 Script de Démonstration Sprint 3 - Laboratoire de Test Hanuman
# Démonstration interactive de toutes les fonctionnalités du laboratoire de test

# Couleurs pour l'affichage
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
NC='\033[0m' # No Color

# Fonctions utilitaires
print_header() {
    echo -e "\n${PURPLE}╔══════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${PURPLE}║${WHITE}                    $1                    ${PURPLE}║${NC}"
    echo -e "${PURPLE}╚══════════════════════════════════════════════════════════════╝${NC}\n"
}

print_info() {
    echo -e "${CYAN}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_step() {
    echo -e "\n${BLUE}🔹 $1${NC}"
}

# Vérifications préalables
check_prerequisites() {
    print_header "🔍 VÉRIFICATION DES PRÉREQUIS"

    # Vérifier Node.js
    if command -v node &> /dev/null; then
        NODE_VERSION=$(node --version)
        print_success "Node.js détecté: $NODE_VERSION"
    else
        print_error "Node.js non trouvé. Installation requise."
        exit 1
    fi

    # Vérifier TypeScript
    if command -v tsc &> /dev/null || command -v npx &> /dev/null; then
        print_success "TypeScript disponible"
    else
        print_warning "TypeScript non trouvé, installation automatique..."
        npm install -g typescript ts-node
    fi

    # Vérifier la structure des fichiers
    if [[ -f "../testing/test_sprint3_integration.ts" ]]; then
        print_success "Fichiers de test Sprint 3 trouvés"
    else
        print_error "Fichiers de test Sprint 3 manquants"
        exit 1
    fi
}

# Installation des dépendances
install_dependencies() {
    print_header "📦 INSTALLATION DES DÉPENDANCES"

    if [[ -f "../../package.json" ]]; then
        print_info "Installation des dépendances npm..."
        cd ../..
        npm install --silent
        cd sandbox/scripts
        print_success "Dépendances installées"
    else
        print_warning "Aucun package.json trouvé, création d'un environnement minimal..."
        npm init -y &> /dev/null
        npm install --save-dev typescript ts-node @types/node &> /dev/null
    fi
}

# Menu principal
show_menu() {
    clear
    print_header "🧪 LABORATOIRE DE TEST HANUMAN - SPRINT 3"
    echo -e "${WHITE}Choisissez une démonstration :${NC}\n"
    echo -e "${CYAN}1.${NC} 🎬 Démonstration Complète"
    echo -e "${CYAN}2.${NC} 🧪 Tests Automatisés Framework"
    echo -e "${CYAN}3.${NC} ⚡ Tests de Performance"
    echo -e "${CYAN}4.${NC} 📊 Métriques de Qualité"
    echo -e "${CYAN}5.${NC} 🔄 Tests d'Intégration"
    echo -e "${CYAN}6.${NC} 📈 Génération de Rapports"
    echo -e "${CYAN}7.${NC} 🚨 Système d'Alertes"
    echo -e "${CYAN}8.${NC} 📋 Rapport de Statut"
    echo -e "${CYAN}9.${NC} 🔧 Vérification Système"
    echo -e "${CYAN}0.${NC} 🚪 Quitter"
    echo ""
}

# Démonstration complète
run_complete_demo() {
    print_header "🎬 DÉMONSTRATION COMPLÈTE SPRINT 3"
    print_info "Lancement de la démonstration complète du laboratoire de test..."

    cd ../testing

    if command -v ts-node &> /dev/null; then
        print_step "Exécution avec ts-node..."
        ts-node -e "
        import { runSprint3IntegrationTests } from './test_sprint3_integration';
        runSprint3IntegrationTests().then(() => {
            console.log('\\n🎉 Démonstration terminée avec succès !');
        }).catch(error => {
            console.error('❌ Erreur lors de la démonstration:', error.message);
        });
        "
    else
        print_step "Compilation et exécution..."
        npx tsc test_sprint3_integration.ts --target es2020 --module commonjs --esModuleInterop
        node -e "
        const { runSprint3IntegrationTests } = require('./test_sprint3_integration.js');
        runSprint3IntegrationTests().then(() => {
            console.log('\\n🎉 Démonstration terminée avec succès !');
        }).catch(error => {
            console.error('❌ Erreur lors de la démonstration:', error.message);
        });
        "
    fi

    cd ../scripts
}

# Tests du framework automatisé
run_automated_testing_demo() {
    print_header "🧪 DÉMONSTRATION FRAMEWORK DE TESTS AUTOMATISÉS"
    print_info "Test du système de tests automatisés..."

    cd ../testing

    ts-node -e "
    import { AutomatedTestingFramework } from './automated_testing_framework';
    import { SandboxInfrastructure } from '../infrastructure/sandbox_infrastructure';
    import { SandboxSecurity } from '../security/sandbox_security';
    import { EnvironmentManager } from '../environments/environment_manager';

    async function demo() {
        console.log('🔧 Initialisation du framework...');
        const mockOrchestrator = { emit: () => {}, on: () => {}, off: () => {} };
        const infrastructure = new SandboxInfrastructure(mockOrchestrator as any);
        const security = new SandboxSecurity(infrastructure);
        const envManager = new EnvironmentManager(infrastructure, security);
        const framework = new AutomatedTestingFramework(infrastructure, security, envManager);

        console.log('📝 Création d\\'une suite de tests...');
        await framework.addTestSuite({
            id: 'demo-suite',
            name: 'Suite de Démonstration',
            description: 'Tests de démonstration',
            tests: [],
            parallel: true,
            maxConcurrency: 2,
            tags: ['demo']
        });

        console.log('✅ Framework de tests automatisés opérationnel !');
        const stats = framework.getGlobalStats();
        console.log('📊 Statistiques:', stats);
    }

    demo().catch(console.error);
    "

    cd ../scripts
}

# Tests de performance
run_performance_demo() {
    print_header "⚡ DÉMONSTRATION TESTS DE PERFORMANCE"
    print_info "Test du système de performance..."

    cd ../testing

    ts-node -e "
    import { PerformanceTestingSystem } from './performance_testing';
    import { SandboxInfrastructure } from '../infrastructure/sandbox_infrastructure';

    async function demo() {
        console.log('🔧 Initialisation du système de performance...');
        const mockOrchestrator = { emit: () => {}, on: () => {}, off: () => {} };
        const infrastructure = new SandboxInfrastructure(mockOrchestrator as any);
        const perfSystem = new PerformanceTestingSystem(infrastructure);

        console.log('🚀 Exécution d\\'un test de performance...');
        const testResult = await perfSystem.executePerformanceTest({
            id: 'demo-perf-test',
            name: 'Test de Performance Démo',
            description: 'Test de démonstration',
            type: 'load',
            configuration: {
                duration: 5,
                rampUpTime: 1,
                rampDownTime: 1,
                virtualUsers: 3,
                requestsPerSecond: 5
            },
            target: { endpoint: '/api/demo' },
            thresholds: {
                maxResponseTime: 200,
                minThroughput: 3,
                maxErrorRate: 10,
                maxCpuUsage: 80,
                maxMemoryUsage: 80
            },
            scenarios: []
        });

        console.log('✅ Test de performance terminé !');
        console.log('📈 Résultat:', testResult.status);
        console.log('⚡ Métriques:', testResult.metrics);
    }

    demo().catch(console.error);
    "

    cd ../scripts
}

# Métriques de qualité
run_quality_metrics_demo() {
    print_header "📊 DÉMONSTRATION MÉTRIQUES DE QUALITÉ"
    print_info "Test du système de métriques de qualité..."

    cd ../testing

    ts-node -e "
    import { QualityMetricsSystem } from './quality_metrics';
    import { AutomatedTestingFramework } from './automated_testing_framework';
    import { PerformanceTestingSystem } from './performance_testing';
    import { SandboxInfrastructure } from '../infrastructure/sandbox_infrastructure';
    import { SandboxSecurity } from '../security/sandbox_security';
    import { EnvironmentManager } from '../environments/environment_manager';

    async function demo() {
        console.log('🔧 Initialisation du système de métriques...');
        const mockOrchestrator = { emit: () => {}, on: () => {}, off: () => {} };
        const infrastructure = new SandboxInfrastructure(mockOrchestrator as any);
        const security = new SandboxSecurity(infrastructure);
        const envManager = new EnvironmentManager(infrastructure, security);
        const testFramework = new AutomatedTestingFramework(infrastructure, security, envManager);
        const perfSystem = new PerformanceTestingSystem(infrastructure);
        const qualitySystem = new QualityMetricsSystem(testFramework, perfSystem, infrastructure);

        console.log('📊 Collecte des métriques de qualité...');
        const metrics = await qualitySystem.collectMetrics('demo-project');

        console.log('✅ Métriques collectées !');
        console.log('🎯 Score global:', metrics.score.overall + '/100 (' + metrics.score.grade + ')');
        console.log('📈 Tendance:', metrics.score.trend);

        console.log('📄 Génération d\\'un rapport...');
        const report = await qualitySystem.generateQualityReport('demo-project');
        console.log('✅ Rapport généré avec', Object.keys(report.metrics).length, 'métriques');
    }

    demo().catch(console.error);
    "

    cd ../scripts
}

# Tests d'intégration
run_integration_demo() {
    print_header "🔄 DÉMONSTRATION TESTS D'INTÉGRATION"
    print_info "Test du workflow d'intégration complet..."

    cd ../testing

    ts-node -e "
    import { Sprint3IntegrationTest } from './test_sprint3_integration';

    async function demo() {
        console.log('🔧 Initialisation des tests d\\'intégration...');
        const integrationTest = new Sprint3IntegrationTest();

        console.log('🚀 Exécution des tests d\\'intégration...');
        const results = await integrationTest.runAllTests();

        console.log('\\n📋 RÉSULTATS FINAUX');
        console.log('✅ Tests réussis:', results.passed);
        console.log('❌ Tests échoués:', results.failed);
        console.log('📊 Taux de réussite:', ((results.passed / (results.passed + results.failed)) * 100).toFixed(1) + '%');

        if (results.failed === 0) {
            console.log('🏆 TOUS LES TESTS SONT PASSÉS !');
        }
    }

    demo().catch(console.error);
    "

    cd ../scripts
}

# Génération de rapports
run_reports_demo() {
    print_header "📈 DÉMONSTRATION GÉNÉRATION DE RAPPORTS"
    print_info "Test de la génération de rapports..."

    cd ../testing

    ts-node -e "
    import { QualityMetricsSystem } from './quality_metrics';
    import { AutomatedTestingFramework } from './automated_testing_framework';
    import { PerformanceTestingSystem } from './performance_testing';
    import { SandboxInfrastructure } from '../infrastructure/sandbox_infrastructure';
    import { SandboxSecurity } from '../security/sandbox_security';
    import { EnvironmentManager } from '../environments/environment_manager';

    async function demo() {
        console.log('🔧 Initialisation du système...');
        const mockOrchestrator = { emit: () => {}, on: () => {}, off: () => {} };
        const infrastructure = new SandboxInfrastructure(mockOrchestrator as any);
        const security = new SandboxSecurity(infrastructure);
        const envManager = new EnvironmentManager(infrastructure, security);
        const testFramework = new AutomatedTestingFramework(infrastructure, security, envManager);
        const perfSystem = new PerformanceTestingSystem(infrastructure);
        const qualitySystem = new QualityMetricsSystem(testFramework, perfSystem, infrastructure);

        console.log('📄 Génération de rapports en différents formats...');

        const formats = ['json', 'csv', 'html'];
        for (const format of formats) {
            console.log('📝 Génération rapport', format.toUpperCase() + '...');
            const report = await qualitySystem.exportMetrics('demo-project', format as any);
            console.log('✅ Rapport', format.toUpperCase(), 'généré (' + report.length + ' caractères)');
        }

        console.log('🎉 Tous les rapports ont été générés avec succès !');
    }

    demo().catch(console.error);
    "

    cd ../scripts
}

# Système d'alertes
run_alerts_demo() {
    print_header "🚨 DÉMONSTRATION SYSTÈME D'ALERTES"
    print_info "Test du système d'alertes..."

    cd ../testing

    ts-node -e "
    import { QualityMetricsSystem } from './quality_metrics';
    import { AutomatedTestingFramework } from './automated_testing_framework';
    import { PerformanceTestingSystem } from './performance_testing';
    import { SandboxInfrastructure } from '../infrastructure/sandbox_infrastructure';
    import { SandboxSecurity } from '../security/sandbox_security';
    import { EnvironmentManager } from '../environments/environment_manager';

    async function demo() {
        console.log('🔧 Initialisation du système d\\'alertes...');
        const mockOrchestrator = { emit: () => {}, on: () => {}, off: () => {} };
        const infrastructure = new SandboxInfrastructure(mockOrchestrator as any);
        const security = new SandboxSecurity(infrastructure);
        const envManager = new EnvironmentManager(infrastructure, security);
        const testFramework = new AutomatedTestingFramework(infrastructure, security, envManager);
        const perfSystem = new PerformanceTestingSystem(infrastructure);
        const qualitySystem = new QualityMetricsSystem(testFramework, perfSystem, infrastructure);

        console.log('📊 Collecte de métriques pour déclencher des alertes...');
        await qualitySystem.collectMetrics('alert-test-project');

        console.log('🚨 Vérification des alertes actives...');
        const alerts = qualitySystem.getActiveAlerts();

        console.log('✅ Système d\\'alertes opérationnel !');
        console.log('📊 Nombre d\\'alertes détectées:', alerts.length);

        if (alerts.length > 0) {
            const alertTypes = new Set(alerts.map(alert => alert.type));
            const severities = new Set(alerts.map(alert => alert.severity));
            console.log('📋 Types d\\'alertes:', Array.from(alertTypes).join(', '));
            console.log('⚠️ Niveaux de sévérité:', Array.from(severities).join(', '));
        }
    }

    demo().catch(console.error);
    "

    cd ../scripts
}

# Rapport de statut
show_status_report() {
    print_header "📋 RAPPORT DE STATUT SPRINT 3"

    echo -e "${WHITE}🎯 COMPOSANTS IMPLÉMENTÉS${NC}"
    echo -e "${GREEN}✅ Framework de Tests Automatisés${NC}"
    echo -e "${GREEN}✅ Système de Tests de Performance${NC}"
    echo -e "${GREEN}✅ Système de Métriques de Qualité${NC}"
    echo -e "${GREEN}✅ Générateur de Tests Intelligent${NC}"
    echo -e "${GREEN}✅ Simulateur de Charge${NC}"
    echo -e "${GREEN}✅ Interface Laboratoire de Test${NC}"
    echo -e "${GREEN}✅ Système de Rapports Multi-formats${NC}"
    echo -e "${GREEN}✅ Système d'Alertes en Temps Réel${NC}"
    echo -e "${GREEN}✅ Tests d'Intégration Complets${NC}"
    echo -e "${GREEN}✅ Scripts de Démonstration${NC}"

    echo -e "\n${WHITE}📊 MÉTRIQUES CLÉS${NC}"
    echo -e "${CYAN}• Tests automatisés : Unitaires, Intégration, Régression${NC}"
    echo -e "${CYAN}• Tests de performance : Charge, Stress, Spike, Volume, Endurance${NC}"
    echo -e "${CYAN}• Métriques de qualité : Score global 0-100 avec grade A-F${NC}"
    echo -e "${CYAN}• Formats de rapport : JSON, CSV, HTML${NC}"
    echo -e "${CYAN}• Types d'alertes : Threshold, Regression, Anomaly${NC}"
    echo -e "${CYAN}• Niveaux de sévérité : Info, Warning, Error, Critical${NC}"

    echo -e "\n${WHITE}🏆 STATUT GLOBAL${NC}"
    echo -e "${GREEN}✅ SPRINT 3 TERMINÉ AVEC SUCCÈS${NC}"
    echo -e "${BLUE}🚀 PRÊT POUR SPRINT 4 - VALIDATION SÉCURITÉ${NC}"

    echo -e "\n${WHITE}📁 FICHIERS PRINCIPAUX${NC}"
    echo -e "${CYAN}• automated_testing_framework.ts - Framework de tests${NC}"
    echo -e "${CYAN}• performance_testing.ts - Tests de performance${NC}"
    echo -e "${CYAN}• quality_metrics.tsx - Métriques de qualité${NC}"
    echo -e "${CYAN}• test_generator.tsx - Générateur de tests${NC}"
    echo -e "${CYAN}• load_simulator.tsx - Simulateur de charge${NC}"
    echo -e "${CYAN}• test_lab_interface.tsx - Interface utilisateur${NC}"
    echo -e "${CYAN}• test_sprint3_integration.ts - Tests d'intégration${NC}"
}

# Vérification système
check_system() {
    print_header "🔧 VÉRIFICATION SYSTÈME"

    print_step "Vérification de la structure des fichiers..."

    local files=(
        "../testing/automated_testing_framework.ts"
        "../testing/performance_testing.ts"
        "../testing/quality_metrics.tsx"
        "../testing/test_generator.tsx"
        "../testing/load_simulator.tsx"
        "../testing/test_sprint3_integration.ts"
        "../interfaces/test_lab_interface.tsx"
    )

    local missing_files=0

    for file in "${files[@]}"; do
        if [[ -f "$file" ]]; then
            print_success "$(basename "$file")"
        else
            print_error "$(basename "$file") - MANQUANT"
            ((missing_files++))
        fi
    done

    echo ""
    if [[ $missing_files -eq 0 ]]; then
        print_success "Tous les fichiers sont présents !"
        print_info "Le Sprint 3 est prêt pour la démonstration."
    else
        print_warning "$missing_files fichier(s) manquant(s)"
        print_info "Vérifiez l'installation du Sprint 3."
    fi

    print_step "Vérification des dépendances TypeScript..."
    if command -v tsc &> /dev/null; then
        print_success "TypeScript compiler disponible"
    else
        print_warning "TypeScript compiler non trouvé"
    fi

    if command -v ts-node &> /dev/null; then
        print_success "ts-node disponible"
    else
        print_warning "ts-node non trouvé"
    fi
}

# Boucle principale
main() {
    # Vérifications initiales
    check_prerequisites
    install_dependencies

    while true; do
        show_menu
        read -p "Votre choix (0-9): " choice

        case $choice in
            1)
                run_complete_demo
                ;;
            2)
                run_automated_testing_demo
                ;;
            3)
                run_performance_demo
                ;;
            4)
                run_quality_metrics_demo
                ;;
            5)
                run_integration_demo
                ;;
            6)
                run_reports_demo
                ;;
            7)
                run_alerts_demo
                ;;
            8)
                show_status_report
                ;;
            9)
                check_system
                ;;
            0)
                print_header "🙏 AUM HANUMAN LABORATOIRE DE TEST NAMAHA"
                echo -e "${CYAN}Merci d'avoir exploré le Sprint 3 !${NC}"
                echo -e "${PURPLE}✨ Le laboratoire de test est prêt pour l'évolution continue ✨${NC}"
                exit 0
                ;;
            *)
                print_error "Option invalide. Choisissez entre 0 et 9."
                ;;
        esac

        echo ""
        read -p "Appuyez sur Entrée pour continuer..."
        clear
    done
}

# Point d'entrée du script
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi