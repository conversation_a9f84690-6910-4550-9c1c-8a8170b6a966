{"version": 3, "file": "resolving-load-balancer.js", "sourceRoot": "", "sources": ["../../src/resolving-load-balancer.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;GAeG;;;AAEH,mDAKyB;AACzB,qDAI0B;AAC1B,6DAAyD;AACzD,yCAAsE;AAEtE,qCAAkE;AAClE,uDAAmE;AACnE,2CAAqC;AAErC,yCAAsC;AACtC,qCAAqC;AACrC,2CAA2C;AAE3C,6CAAoD;AACpD,+EAAyE;AAGzE,MAAM,WAAW,GAAG,yBAAyB,CAAC;AAE9C,SAAS,KAAK,CAAC,IAAY;IACzB,OAAO,CAAC,KAAK,CAAC,wBAAY,CAAC,KAAK,EAAE,WAAW,EAAE,IAAI,CAAC,CAAC;AACvD,CAAC;AAID;;;GAGG;AACH,MAAM,sBAAsB,GAAqB;IAC/C,oBAAoB;IACpB,SAAS;IACT,OAAO;CACR,CAAC;AAEF,SAAS,eAAe,CACtB,OAAe,EACf,MAAc,EACd,YAA0B,EAC1B,UAA0B;IAE1B,KAAK,MAAM,IAAI,IAAI,YAAY,CAAC,IAAI,EAAE,CAAC;QACrC,QAAQ,UAAU,EAAE,CAAC;YACnB,KAAK,OAAO;gBACV,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;oBAClC,OAAO,IAAI,CAAC;gBACd,CAAC;gBACD,MAAM;YACR,KAAK,SAAS;gBACZ,IAAI,IAAI,CAAC,OAAO,KAAK,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;oBAC7C,OAAO,IAAI,CAAC;gBACd,CAAC;gBACD,MAAM;YACR,KAAK,oBAAoB;gBACvB,IAAI,IAAI,CAAC,OAAO,KAAK,OAAO,IAAI,IAAI,CAAC,MAAM,KAAK,MAAM,EAAE,CAAC;oBACvD,OAAO,IAAI,CAAC;gBACd,CAAC;QACL,CAAC;IACH,CAAC;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAS,kBAAkB,CACzB,OAAe,EACf,MAAc,EACd,aAA6B,EAC7B,UAA0B;IAE1B,KAAK,MAAM,MAAM,IAAI,aAAa,EAAE,CAAC;QACnC,IAAI,eAAe,CAAC,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,UAAU,CAAC,EAAE,CAAC;YACzD,OAAO,MAAM,CAAC;QAChB,CAAC;IACH,CAAC;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAED,SAAS,wBAAwB,CAC/B,aAAmC;IAEnC,OAAO;QACH,MAAM,CACN,UAAkB,EAClB,QAAkB;;YAElB,MAAM,SAAS,GAAG,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YAClE,MAAM,OAAO,GAAG,MAAA,SAAS,CAAC,CAAC,CAAC,mCAAI,EAAE,CAAC;YACnC,MAAM,MAAM,GAAG,MAAA,SAAS,CAAC,CAAC,CAAC,mCAAI,EAAE,CAAC;YAClC,IAAI,aAAa,IAAI,aAAa,CAAC,YAAY,EAAE,CAAC;gBAChD;;;;;kBAKE;gBACF,KAAK,MAAM,UAAU,IAAI,sBAAsB,EAAE,CAAC;oBAChD,MAAM,cAAc,GAAG,kBAAkB,CACvC,OAAO,EACP,MAAM,EACN,aAAa,CAAC,YAAY,EAC1B,UAAU,CACX,CAAC;oBACF,IAAI,cAAc,EAAE,CAAC;wBACnB,OAAO;4BACL,YAAY,EAAE,cAAc;4BAC5B,eAAe,EAAE,EAAE;4BACnB,MAAM,EAAE,kBAAM,CAAC,EAAE;4BACjB,sBAAsB,EAAE,EAAE;yBAC3B,CAAC;oBACJ,CAAC;gBACH,CAAC;YACH,CAAC;YACD,OAAO;gBACL,YAAY,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE;gBAC1B,eAAe,EAAE,EAAE;gBACnB,MAAM,EAAE,kBAAM,CAAC,EAAE;gBACjB,sBAAsB,EAAE,EAAE;aAC3B,CAAC;QACJ,CAAC;QACD,KAAK,KAAI,CAAC;KACX,CAAC;AACJ,CAAC;AAUD,MAAa,qBAAqB;IAiChC;;;;;;;;;;;OAWG;IACH,YACmB,MAAe,EACf,oBAA0C,EAC1C,cAA8B,EAC9B,sBAA0C,EAC1C,kBAA6C;QAJ7C,WAAM,GAAN,MAAM,CAAS;QACf,yBAAoB,GAApB,oBAAoB,CAAsB;QAC1C,mBAAc,GAAd,cAAc,CAAgB;QAC9B,2BAAsB,GAAtB,sBAAsB,CAAoB;QAC1C,uBAAkB,GAAlB,kBAAkB,CAA2B;QA3CxD,qBAAgB,GAAsB,sCAAiB,CAAC,IAAI,CAAC;QAC7D,sBAAiB,GAAW,IAAI,oBAAW,CAAC,IAAI,CAAC,CAAC;QAClD,4BAAuB,GAAkB,IAAI,CAAC;QACtD;;WAEG;QACK,iBAAY,GAAsB,sCAAiB,CAAC,IAAI,CAAC;QAEjE;;;;WAIG;QACK,0BAAqB,GAAyB,IAAI,CAAC;QAO3D;;;WAGG;QACK,sBAAiB,GAAG,KAAK,CAAC;QAqBhC,IAAI,cAAc,CAAC,qBAAqB,CAAC,EAAE,CAAC;YAC1C,IAAI,CAAC,oBAAoB,GAAG,IAAA,sCAAqB,EAC/C,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,qBAAqB,CAAE,CAAC,CACnD,CAAC;QACJ,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,oBAAoB,GAAG;gBAC1B,mBAAmB,EAAE,EAAE;gBACvB,YAAY,EAAE,EAAE;aACjB,CAAC;QACJ,CAAC;QAED,IAAI,CAAC,WAAW,CAAC,sCAAiB,CAAC,IAAI,EAAE,IAAI,oBAAW,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC;QACtE,IAAI,CAAC,iBAAiB,GAAG,IAAI,sDAAwB,CACnD;YACE,gBAAgB,EACd,oBAAoB,CAAC,gBAAgB,CAAC,IAAI,CAAC,oBAAoB,CAAC;YAClE,mBAAmB,EAAE,GAAG,EAAE;gBACxB;;;sCAGsB;gBACtB,IAAI,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,EAAE,CAAC;oBACpC,KAAK,CACH,qDAAqD;wBACnD,IAAI,CAAC,cAAc,CAAC,UAAU,EAAE,CAAC,WAAW,EAAE,CACjD,CAAC;oBACF,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;gBAChC,CAAC;qBAAM,CAAC;oBACN,IAAI,CAAC,gBAAgB,EAAE,CAAC;gBAC1B,CAAC;YACH,CAAC;YACD,WAAW,EAAE,CAAC,QAA2B,EAAE,MAAc,EAAE,YAA2B,EAAE,EAAE;gBACxF,IAAI,CAAC,gBAAgB,GAAG,QAAQ,CAAC;gBACjC,IAAI,CAAC,iBAAiB,GAAG,MAAM,CAAC;gBAChC,IAAI,CAAC,uBAAuB,GAAG,YAAY,CAAC;gBAC5C,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,MAAM,EAAE,YAAY,CAAC,CAAC;YACnD,CAAC;YACD,gBAAgB,EACd,oBAAoB,CAAC,gBAAgB,CAAC,IAAI,CAAC,oBAAoB,CAAC;YAClE,mBAAmB,EACjB,oBAAoB,CAAC,mBAAmB,CAAC,IAAI,CAAC,oBAAoB,CAAC;SACtE,CACF,CAAC;QACF,IAAI,CAAC,aAAa,GAAG,IAAA,yBAAc,EACjC,MAAM,EACN;YACE,sBAAsB,EAAE,CACtB,YAAwB,EACxB,aAAmC,EACnC,kBAAuC,EACvC,cAAqC,EACrC,UAAsC,EACtC,EAAE;;gBACF,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,CAAC;gBAC3B,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC;gBAC5B,IAAI,oBAAoB,GAAyB,IAAI,CAAC;gBACtD;;;mBAGG;gBACH,IAAI,aAAa,KAAK,IAAI,EAAE,CAAC;oBAC3B,eAAe;oBACf,IAAI,kBAAkB,KAAK,IAAI,EAAE,CAAC;wBAChC,SAAS;wBACT,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC;wBAClC,oBAAoB,GAAG,IAAI,CAAC,oBAAoB,CAAC;oBACnD,CAAC;yBAAM,CAAC;wBACN,SAAS;wBACT,IAAI,IAAI,CAAC,qBAAqB,KAAK,IAAI,EAAE,CAAC;4BACxC,YAAY;4BACZ,IAAI,CAAC,uBAAuB,CAAC,kBAAkB,CAAC,CAAC;wBACnD,CAAC;6BAAM,CAAC;4BACN,WAAW;4BACX,oBAAoB,GAAG,IAAI,CAAC,qBAAqB,CAAC;wBACpD,CAAC;oBACH,CAAC;gBACH,CAAC;qBAAM,CAAC;oBACN,SAAS;oBACT,oBAAoB,GAAG,aAAa,CAAC;oBACrC,IAAI,CAAC,qBAAqB,GAAG,aAAa,CAAC;gBAC7C,CAAC;gBACD,MAAM,iBAAiB,GACrB,MAAA,oBAAoB,aAApB,oBAAoB,uBAApB,oBAAoB,CAAE,mBAAmB,mCAAI,EAAE,CAAC;gBAClD,MAAM,mBAAmB,GAAG,IAAA,sCAAsB,EAChD,iBAAiB,EACjB,IAAI,CACL,CAAC;gBACF,IAAI,mBAAmB,KAAK,IAAI,EAAE,CAAC;oBACjC,gGAAgG;oBAChG,IAAI,CAAC,uBAAuB,CAAC;wBAC3B,IAAI,EAAE,kBAAM,CAAC,WAAW;wBACxB,OAAO,EACL,gEAAgE;wBAClE,QAAQ,EAAE,IAAI,mBAAQ,EAAE;qBACzB,CAAC,CAAC;oBACH,cAAc,aAAd,cAAc,uBAAd,cAAc,CAAE,KAAK,EAAE,CAAC;oBACxB,OAAO;gBACT,CAAC;gBACD,IAAI,CAAC,iBAAiB,CAAC,iBAAiB,CACtC,YAAY,EACZ,mBAAmB,kCACf,IAAI,CAAC,cAAc,GAAK,UAAU,EACvC,CAAC;gBACF,MAAM,kBAAkB,GACtB,oBAAoB,aAApB,oBAAoB,cAApB,oBAAoB,GAAI,IAAI,CAAC,oBAAoB,CAAC;gBACpD,IAAI,CAAC,sBAAsB,CACzB,kBAAkB,EAClB,cAAc,aAAd,cAAc,cAAd,cAAc,GAAI,wBAAwB,CAAC,kBAAkB,CAAC,CAC/D,CAAC;YACJ,CAAC;YACD,OAAO,EAAE,CAAC,KAAmB,EAAE,EAAE;gBAC/B,IAAI,CAAC,uBAAuB,CAAC,KAAK,CAAC,CAAC;YACtC,CAAC;SACF,EACD,cAAc,CACf,CAAC;QACF,MAAM,cAAc,GAAmB;YACrC,YAAY,EAAE,cAAc,CAAC,mCAAmC,CAAC;YACjE,QAAQ,EAAE,cAAc,CAAC,+BAA+B,CAAC;SAC1D,CAAC;QACF,IAAI,CAAC,cAAc,GAAG,IAAI,gCAAc,CAAC,GAAG,EAAE;YAC5C,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;gBAC3B,IAAI,CAAC,gBAAgB,EAAE,CAAC;gBACxB,IAAI,CAAC,iBAAiB,GAAG,KAAK,CAAC;YACjC,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,uBAAuB,CAAC,CAAC;YAChG,CAAC;QACH,CAAC,EAAE,cAAc,CAAC,CAAC;QACnB,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC;IAC9B,CAAC;IAEO,gBAAgB;QACtB,IAAI,CAAC,aAAa,CAAC,gBAAgB,EAAE,CAAC;QACtC,IAAI,IAAI,CAAC,YAAY,KAAK,sCAAiB,CAAC,IAAI,EAAE,CAAC;YACjD;;;qCAGyB;YACzB,IAAI,CAAC,WAAW,CAAC,sCAAiB,CAAC,UAAU,EAAE,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,uBAAuB,CAAC,CAAC;QACvG,CAAC;QACD,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,CAAC;IAChC,CAAC;IAEO,WAAW,CAAC,iBAAoC,EAAE,MAAc,EAAE,YAA2B;QACnG,KAAK,CACH,IAAA,wBAAW,EAAC,IAAI,CAAC,MAAM,CAAC;YACtB,GAAG;YACH,sCAAiB,CAAC,IAAI,CAAC,YAAY,CAAC;YACpC,MAAM;YACN,sCAAiB,CAAC,iBAAiB,CAAC,CACvC,CAAC;QACF,sDAAsD;QACtD,IAAI,iBAAiB,KAAK,sCAAiB,CAAC,IAAI,EAAE,CAAC;YACjD,MAAM,GAAG,IAAI,oBAAW,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;QACzC,CAAC;QACD,IAAI,CAAC,YAAY,GAAG,iBAAiB,CAAC;QACtC,IAAI,CAAC,oBAAoB,CAAC,WAAW,CAAC,iBAAiB,EAAE,MAAM,EAAE,YAAY,CAAC,CAAC;IACjF,CAAC;IAEO,uBAAuB,CAAC,KAAmB;QACjD,IAAI,IAAI,CAAC,gBAAgB,KAAK,sCAAiB,CAAC,IAAI,EAAE,CAAC;YACrD,IAAI,CAAC,WAAW,CACd,sCAAiB,CAAC,iBAAiB,EACnC,IAAI,0BAAiB,CAAC,KAAK,CAAC,EAC5B,KAAK,CAAC,OAAO,CACd,CAAC;YACF,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;QACjC,CAAC;IACH,CAAC;IAED,QAAQ;QACN,IACE,IAAI,CAAC,YAAY,KAAK,sCAAiB,CAAC,IAAI;YAC5C,IAAI,CAAC,YAAY,KAAK,sCAAiB,CAAC,iBAAiB,EACzD,CAAC;YACD,IAAI,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,EAAE,CAAC;gBACpC,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;YAChC,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC1B,CAAC;QACH,CAAC;QACD,IAAI,CAAC,iBAAiB,CAAC,QAAQ,EAAE,CAAC;IACpC,CAAC;IAED,iBAAiB,CACf,YAAwB,EACxB,QAAyC;QAEzC,MAAM,IAAI,KAAK,CAAC,0DAA0D,CAAC,CAAC;IAC9E,CAAC;IAED,YAAY;QACV,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC;QAC5B,IAAI,CAAC,iBAAiB,CAAC,YAAY,EAAE,CAAC;IACxC,CAAC;IAED,OAAO;QACL,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,CAAC;QACjC,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC;QAC7B,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC;QAC5B,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,CAAC;QAC3B,IAAI,CAAC,gBAAgB,GAAG,sCAAiB,CAAC,IAAI,CAAC;QAC/C,IAAI,CAAC,iBAAiB,GAAG,IAAI,oBAAW,CAAC,IAAI,CAAC,CAAC;QAC/C,IAAI,CAAC,YAAY,GAAG,sCAAiB,CAAC,IAAI,CAAC;QAC3C,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC;QAClC,IAAI,CAAC,iBAAiB,GAAG,KAAK,CAAC;IACjC,CAAC;IAED,WAAW;QACT,OAAO,yBAAyB,CAAC;IACnC,CAAC;CACF;AAvQD,sDAuQC"}