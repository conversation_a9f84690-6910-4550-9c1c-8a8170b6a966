# contributing to `cors`

CORS is a node.js package for providing a [connect](http://www.senchalabs.org/connect/)/[express](http://expressjs.com/) middleware that can be used to enable [CORS](http://en.wikipedia.org/wiki/Cross-origin_resource_sharing) with various options. Learn more about the project in [the README](README.md).

## The CORS Spec

[http://www.w3.org/TR/cors/](http://www.w3.org/TR/cors/)

## Pull Requests Welcome

* Include `'use strict';` in every javascript file.
* 2 space indentation.
* Please run the testing steps below before submitting.

## Testing

```bash
$ npm install
$ npm test
```

## Interactive Testing Harness

[http://node-cors-client.herokuapp.com](http://node-cors-client.herokuapp.com)

Related git repositories:

* [https://github.com/TroyGoode/node-cors-server](https://github.com/TroyGoode/node-cors-server)
* [https://github.com/TroyGoode/node-cors-client](https://github.com/TroyGoode/node-cors-client)

## License

[MIT License](http://www.opensource.org/licenses/mit-license.php)
